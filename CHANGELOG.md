# Changelog

All notable changes to this project will be documented in this file.
See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## v0.1.0

[compare changes](https://github.com/kdt-sol/solana-grpc-client/compare/v0.0.3...v0.1.0)

### 🚀 Enhancements

- Optimize ([6b9928b](https://github.com/kdt-sol/solana-grpc-client/commit/6b9928b))
- ⚠️  Remove thor stream and add orbit jetstream and yellowstone geyser ([2049206](https://github.com/kdt-sol/solana-grpc-client/commit/2049206))
- Support thor stream ([8104679](https://github.com/kdt-sol/solana-grpc-client/commit/8104679))

### 💅 Refactors

- Improve constructor formatting and timeout handling in DuplexStreamIterator ([64afdb3](https://github.com/kdt-sol/solana-grpc-client/commit/64afdb3))

### 📖 Documentation

- Update README for clarity and add ThorStreamer details ([ee7970f](https://github.com/kdt-sol/solana-grpc-client/commit/ee7970f))

### 🏡 Chore

- Update deps ([8f8ce73](https://github.com/kdt-sol/solana-grpc-client/commit/8f8ce73))
- **workflows:** Add install protoc action ([db8723a](https://github.com/kdt-sol/solana-grpc-client/commit/db8723a))
- Update deps ([3195502](https://github.com/kdt-sol/solana-grpc-client/commit/3195502))
- Add submodules ([7a3daec](https://github.com/kdt-sol/solana-grpc-client/commit/7a3daec))
- Update submodule commits and clean up .gitignore ([e97fbef](https://github.com/kdt-sol/solana-grpc-client/commit/e97fbef))
- Update ESLint config to ignore README.md ([2ad1a60](https://github.com/kdt-sol/solana-grpc-client/commit/2ad1a60))
- Enable submodule checkout in CI workflow ([0c40753](https://github.com/kdt-sol/solana-grpc-client/commit/0c40753))
- Add thor-streamer submodule ([bf4eebc](https://github.com/kdt-sol/solana-grpc-client/commit/bf4eebc))

#### ⚠️ Breaking Changes

- ⚠️  Remove thor stream and add orbit jetstream and yellowstone geyser ([2049206](https://github.com/kdt-sol/solana-grpc-client/commit/2049206))

### ❤️ Contributors

- DiepPk <<EMAIL>>

## v0.0.3

[compare changes](https://github.com/kdt-sol/thorstream-client/compare/v0.0.2...v0.0.3)

### 🩹 Fixes

- Check cancelled error ([30213d6](https://github.com/kdt-sol/thorstream-client/commit/30213d6))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.2

[compare changes](https://github.com/kdt-sol/thorstream-client/compare/v0.0.1...v0.0.2)

### 🩹 Fixes

- Data formatter ([1eb79fb](https://github.com/kdt-sol/thorstream-client/commit/1eb79fb))

### 🏡 Chore

- Update deps ([7b41c98](https://github.com/kdt-sol/thorstream-client/commit/7b41c98))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

## v0.0.1


### 🏡 Chore

- Initial commit ([c0017c9](https://github.com/kdt-sol/thorstream-client/commit/c0017c9))

### ❤️ Contributors

- Diep Dang <<EMAIL>>

